-- Valic Loading Screen Client
-- Author: stepan_valic

local isLoadingFinished = false
local currentProgress = 0

-- Funkce pro aktualizaci progress baru
local function updateLoadingProgress(progress, text)
    if not isLoadingFinished then
        currentProgress = progress
        SendNUIMessage({
            type = 'updateProgress',
            progress = progress,
            text = text or nil
        })
    end
end

-- Funkce pro dokončení loading screenu
local function finishLoading()
    if not isLoadingFinished then
        isLoadingFinished = true
        updateLoadingProgress(100, "Vítejte v Diverse RP!")
        
        -- Počkej chvilku před vypnutím loading screenu
        Citizen.Wait(2000)
        
        -- Vypni loading screen
        ShutdownLoadingScreen()
        ShutdownLoadingScreenNui()
        
        -- <PERSON><PERSON><PERSON> zprávu do chatu
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = true,
            args = {"[Diverse RP]", "Úspěšně jste se připojili na server!"}
        })
    end
end

-- Event handlery pro loading progress
AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Inicializace loading screenu
        updateLoadingProgress(0, "Inicializace...")
    end
end)

-- Sledování loading progress
Citizen.CreateThread(function()
    -- Počkej na spuštění resource
    while GetResourceState(GetCurrentResourceName()) ~= 'started' do
        Citizen.Wait(100)
    end

    -- Počáteční inicializace
    Citizen.Wait(500)
    updateLoadingProgress(0, "Inicializace...")

    local loadingSteps = {
        {progress = 5, text = "Spouštím systémy...", delay = 800},
        {progress = 15, text = "Načítám mapu...", delay = 1500},
        {progress = 25, text = "Mapa načtena...", delay = 1000},
        {progress = 35, text = "Připojuji session...", delay = 1200},
        {progress = 45, text = "Načítám prohlížeč...", delay = 1000},
        {progress = 55, text = "Načítám detaily...", delay = 1300},
        {progress = 65, text = "Dokončuji session...", delay = 900},
        {progress = 75, text = "Připravuji svět...", delay = 1200},
        {progress = 85, text = "Svět připraven...", delay = 1000},
        {progress = 95, text = "Dokončuji...", delay = 800}
    }

    -- Projdi všechny loading kroky
    for _, step in ipairs(loadingSteps) do
        if not isLoadingFinished then
            updateLoadingProgress(step.progress, step.text)
            Citizen.Wait(step.delay)
        end
    end

    -- Počkej na dokončení načítání hry
    local maxWaitTime = 0
    while not NetworkIsSessionStarted() and maxWaitTime < 30000 do
        Citizen.Wait(100)
        maxWaitTime = maxWaitTime + 100
    end

    -- Dodatečné čekání na stabilizaci
    Citizen.Wait(1000)

    -- Dokončení loading
    if not isLoadingFinished then
        finishLoading()
    end
end)

-- Event pro manuální aktualizaci progress baru
RegisterNetEvent('valic_loading:updateProgress')
AddEventHandler('valic_loading:updateProgress', function(progress, text)
    updateLoadingProgress(progress, text)
end)

-- Event pro dokončení loading screenu
RegisterNetEvent('valic_loading:finishLoading')
AddEventHandler('valic_loading:finishLoading', function()
    finishLoading()
end)

-- Sledování stavu hráče (backup systém)
Citizen.CreateThread(function()
    local maxWaitTime = 0
    while not isLoadingFinished and maxWaitTime < 45000 do -- Max 45 sekund
        -- Zkontroluj, jestli je hráč plně načten
        if NetworkIsPlayerActive(PlayerId()) and not IsPlayerSwitchInProgress() then
            -- Hráč je připraven, ale počkej na hlavní loading thread
            Citizen.Wait(2000)
            if not isLoadingFinished then
                print("^3[Valic Loading] ^7Backup systém dokončuje loading...")
                finishLoading()
            end
            break
        end
        Citizen.Wait(1000)
        maxWaitTime = maxWaitTime + 1000
    end

    -- Nouzové dokončení po 45 sekundách
    if not isLoadingFinished then
        print("^1[Valic Loading] ^7Nouzové dokončení loading screenu!")
        finishLoading()
    end
end)

-- Debug příkazy (pouze pro vývoj)
if GetConvar('valic_loading_debug', 'false') == 'true' then
    RegisterCommand('loading_progress', function(source, args)
        local progress = tonumber(args[1]) or 50
        local text = args[2] or nil
        updateLoadingProgress(progress, text)
    end, false)
    
    RegisterCommand('loading_finish', function()
        finishLoading()
    end, false)
end
