-- Valic Loading Screen Client
-- Author: stepan_valic

local isLoadingFinished = false
local currentProgress = 0

-- Funkce pro aktualizaci progress baru
local function updateLoadingProgress(progress, text)
    if not isLoadingFinished then
        currentProgress = progress
        SendNUIMessage({
            type = 'updateProgress',
            progress = progress,
            text = text or nil
        })
    end
end

-- Funkce pro dokončení loading screenu
local function finishLoading()
    if not isLoadingFinished then
        isLoadingFinished = true
        updateLoadingProgress(100, "Vítejte v Diverse RP!")
        
        -- Počkej chvilku před vypnutím loading screenu
        Citizen.Wait(2000)
        
        -- Vypni loading screen
        ShutdownLoadingScreen()
        ShutdownLoadingScreenNui()
        
        -- <PERSON><PERSON><PERSON> zprávu do chatu
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = true,
            args = {"[Diverse RP]", "Úspěšně jste se připojili na server!"}
        })
    end
end

-- Event handlery pro loading progress
AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Inicializace loading screenu
        updateLoadingProgress(0, "Inicializace...")
    end
end)

-- Sledování loading progress
Citizen.CreateThread(function()
    local loadingSteps = {
        {progress = 5, text = "Spouštím systémy...", delay = 500},
        {progress = 15, text = "Načítám mapu...", delay = 1000},
        {progress = 25, text = "Mapa načtena...", delay = 800},
        {progress = 35, text = "Připojuji session...", delay = 1200},
        {progress = 45, text = "Načítám prohlížeč...", delay = 900},
        {progress = 55, text = "Načítám detaily...", delay = 1100},
        {progress = 65, text = "Dokončuji session...", delay = 700},
        {progress = 75, text = "Připravuji svět...", delay = 1000},
        {progress = 85, text = "Svět připraven...", delay = 800},
        {progress = 95, text = "Dokončuji...", delay = 600}
    }
    
    -- Počkej na inicializaci
    Citizen.Wait(1000)
    
    -- Projdi všechny loading kroky
    for _, step in ipairs(loadingSteps) do
        if not isLoadingFinished then
            updateLoadingProgress(step.progress, step.text)
            Citizen.Wait(step.delay)
        end
    end
    
    -- Počkej na dokončení načítání hry
    while not NetworkIsSessionStarted() do
        Citizen.Wait(100)
    end
    
    -- Dokončení loading
    finishLoading()
end)

-- Event pro manuální aktualizaci progress baru
RegisterNetEvent('valic_loading:updateProgress')
AddEventHandler('valic_loading:updateProgress', function(progress, text)
    updateLoadingProgress(progress, text)
end)

-- Event pro dokončení loading screenu
RegisterNetEvent('valic_loading:finishLoading')
AddEventHandler('valic_loading:finishLoading', function()
    finishLoading()
end)

-- Sledování stavu hráče
Citizen.CreateThread(function()
    while not isLoadingFinished do
        -- Zkontroluj, jestli je hráč plně načten
        if NetworkIsPlayerActive(PlayerId()) and not IsPlayerSwitchInProgress() then
            -- Hráč je připraven, můžeme dokončit loading
            Citizen.Wait(1000) -- Krátké zpoždění pro jistotu
            if not isLoadingFinished then
                finishLoading()
            end
            break
        end
        Citizen.Wait(500)
    end
end)

-- Debug příkazy (pouze pro vývoj)
if GetConvar('valic_loading_debug', 'false') == 'true' then
    RegisterCommand('loading_progress', function(source, args)
        local progress = tonumber(args[1]) or 50
        local text = args[2] or nil
        updateLoadingProgress(progress, text)
    end, false)
    
    RegisterCommand('loading_finish', function()
        finishLoading()
    end, false)
end
