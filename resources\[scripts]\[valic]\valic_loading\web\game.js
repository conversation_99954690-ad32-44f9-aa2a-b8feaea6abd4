// Valic Loading Screen - Game.js
// Author: stepan_valic

// Získání elementů z DOM
const progressBar = document.getElementById('progressBar');
const progressPercentage = document.getElementById('progressPercentage');
const loadingText = document.getElementById('loadingText');

// Loading states podle tvého požadavku
const loadingStates = {
    0: "Inicializace...",
    5: "Spouštím systémy...",
    15: "Načítám mapu...",
    25: "Mapa načtena...",
    35: "Připojuji session...",
    45: "Načítám prohlížeč...",
    55: "Načítám detaily...",
    65: "Dokončuji session...",
    75: "Připravuji svět...",
    85: "Svět připraven...",
    95: "Dokončuji...",
    100: "Vítejte v Diverse RP!"
};

// Funkce pro aktualizaci progress baru
function updateProgress(percentage, customText = null) {
    // Ujisti se, že percentage je v rozmezí 0-100
    percentage = Math.max(0, Math.min(100, percentage));
    
    // Aktualizuj progress bar
    progressBar.style.width = `${percentage}%`;
    progressPercentage.textContent = `${Math.round(percentage)}%`;

    // Použij custom text pokud je poskytnut, jinak použij přednastavené stavy
    if (customText) {
        loadingText.textContent = customText;
    } else {
        let currentText = loadingStates[0];
        for (const perc in loadingStates) {
            if (percentage >= perc) {
                currentText = loadingStates[perc];
            }
        }
        loadingText.textContent = currentText;
    }
    
    // Přidej smooth animaci
    progressBar.style.transition = 'width 0.5s ease-in-out';
}

// Listener pro zprávy z FiveM klienta
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.type) {
        case 'updateProgress':
            updateProgress(data.progress, data.text);
            break;
            
        case 'finishLoading':
            updateProgress(100, "Vítejte v Diverse RP!");
            break;
            
        case 'setCustomText':
            if (loadingText) {
                loadingText.textContent = data.text;
            }
            break;
            
        case 'hideLoading':
            // Fade out efekt před skrytím
            document.body.style.transition = 'opacity 1s ease-out';
            document.body.style.opacity = '0';
            break;
    }
});

// Inicializace při načtení stránky
document.addEventListener('DOMContentLoaded', function() {
    // Nastav počáteční stav
    updateProgress(0);
    
    // Pošli zprávu klientu, že je loading screen připraven
    if (window.invokeNative) {
        window.invokeNative('sendNuiMessage', JSON.stringify({
            type: 'loadingScreenReady'
        }));
    }
    
    console.log('Valic Loading Screen initialized');
});

// Fallback pro případ, že by se loading zasekl
let fallbackTimeout = setTimeout(function() {
    console.warn('Loading screen fallback activated - forcing completion');
    updateProgress(100, "Připojování dokončeno");
}, 30000); // 30 sekund timeout

// Vyčisti timeout pokud se loading dokončí normálně
window.addEventListener('message', function(event) {
    if (event.data.type === 'finishLoading' || event.data.type === 'hideLoading') {
        clearTimeout(fallbackTimeout);
    }
});

// Debug funkce (pouze pro vývoj)
if (window.location.search.includes('debug=true')) {
    window.debugUpdateProgress = updateProgress;
    
    // Testovací sekvence
    window.testLoadingSequence = function() {
        let progress = 0;
        const interval = setInterval(() => {
            progress += 10;
            updateProgress(progress);
            
            if (progress >= 100) {
                clearInterval(interval);
            }
        }, 500);
    };
    
    console.log('Debug mode enabled. Use debugUpdateProgress(percentage, text) or testLoadingSequence()');
}

// Export funkcí pro případné použití
window.ValicLoading = {
    updateProgress: updateProgress,
    loadingStates: loadingStates
};
